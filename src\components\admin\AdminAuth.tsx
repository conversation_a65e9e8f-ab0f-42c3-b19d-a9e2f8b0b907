
import React, { ReactNode, useState, useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate } from 'react-router-dom';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { RefreshCcw } from 'lucide-react';

interface AdminAuthProps {
  children: ReactNode;
}

const AdminAuth: React.FC<AdminAuthProps> = ({ children }) => {
  const { t } = useLanguage();
  const { user, isAuthenticated } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  // Check if user is an admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!isAuthenticated || !user) {
        setIsAdmin(false);
        setIsLoading(false);
        return;
      }
      
      try {
        // Check if the user has admin privileges in the profiles table
        const { data, error } = await supabase
          .from('profiles')
          .select('is_admin')
          .eq('id', user.id)
          .single();
          
        if (error) {
          console.error('Error checking admin status:', error);
          setIsAdmin(false);
          setIsLoading(false);
          return;
        }
        
        setIsAdmin(data?.is_admin === true);
      } catch (error) {
        console.error('Error checking admin status:', error);
        setIsAdmin(false);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkAdminStatus();
  }, [isAuthenticated, user]);
  
  // If not authenticated, redirect to login
  if (!isAuthenticated && !isLoading) {
    toast.error(t('authRequired'), {
      description: t('adminAuthRequired')
    });
    return <Navigate to="/auth" replace />;
  }
  
  // If not an admin, redirect to home
  if (!isAdmin && !isLoading) {
    toast.error(t('accessDenied'), {
      description: t('adminAccessDenied')
    });
    return <Navigate to="/" replace />;
  }
  
  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <main className="flex-grow flex items-center justify-center">
          <div className="flex flex-col items-center gap-2">
            <RefreshCcw className="h-8 w-8 animate-spin text-primary" />
            <p className="text-lg">{t('loading')}</p>
          </div>
        </main>
      </div>
    );
  }
  
  return <>{children}</>;
};

export default AdminAuth;
