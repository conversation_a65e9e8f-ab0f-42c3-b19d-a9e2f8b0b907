
import React from 'react';
import { TabsContent } from '@/components/ui/tabs';
import AdminOverview from './AdminOverview';
import AdminListings from '@/components/admin/AdminListings';
import AdminUsers from '@/components/admin/AdminUsers';
import AdminSettings from '@/components/admin/AdminSettings';

const AdminTabsContent: React.FC = () => {
  return (
    <>
      <TabsContent value="overview" className="space-y-6">
        <AdminOverview />
      </TabsContent>
      
      <TabsContent value="listings">
        <AdminListings />
      </TabsContent>
      
      <TabsContent value="users">
        <AdminUsers />
      </TabsContent>
      
      <TabsContent value="settings">
        <AdminSettings />
      </TabsContent>
    </>
  );
};

export default AdminTabsContent;
