
import React, { useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LayoutDashboard, Package, Users, Settings, BarChart3 } from 'lucide-react';

interface AdminTabsNavProps {
  activeTab: string;
}

const AdminTabsNav: React.FC<AdminTabsNavProps> = ({ activeTab }) => {
  const { t } = useLanguage();

  // Get translated text first
  const overviewText = t('admin.overview');
  const listingsText = t('admin.listings');
  const usersText = t('admin.users');
  const analyticsText = t('admin.analytics');
  const settingsText = t('admin.settings');

  // Debug check for translations
  useEffect(() => {
    console.log('AdminTabsNav translations:', {
      overviewText,
      listingsText,
      usersText,
      analyticsText,
      settingsText
    });
  }, [overviewText, listingsText, usersText, analyticsText, settingsText]);

  return (
    <TabsList className="grid w-full grid-cols-5 h-12">
      <TabsTrigger
        value="overview"
        data-state={activeTab === 'overview' ? 'active' : ''}
        className="flex items-center gap-2"
      >
        <LayoutDashboard className="h-4 w-4" />
        <span className="hidden sm:inline-block">{overviewText}</span>
      </TabsTrigger>

      <TabsTrigger
        value="listings"
        data-state={activeTab === 'listings' ? 'active' : ''}
        className="flex items-center gap-2"
      >
        <Package className="h-4 w-4" />
        <span className="hidden sm:inline-block">{listingsText}</span>
      </TabsTrigger>

      <TabsTrigger
        value="users"
        data-state={activeTab === 'users' ? 'active' : ''}
        className="flex items-center gap-2"
      >
        <Users className="h-4 w-4" />
        <span className="hidden sm:inline-block">{usersText}</span>
      </TabsTrigger>

      <TabsTrigger
        value="analytics"
        data-state={activeTab === 'analytics' ? 'active' : ''}
        className="flex items-center gap-2"
      >
        <BarChart3 className="h-4 w-4" />
        <span className="hidden sm:inline-block">{analyticsText}</span>
      </TabsTrigger>

      <TabsTrigger
        value="settings"
        data-state={activeTab === 'settings' ? 'active' : ''}
        className="flex items-center gap-2"
      >
        <Settings className="h-4 w-4" />
        <span className="hidden sm:inline-block">{settingsText}</span>
      </TabsTrigger>
    </TabsList>
  );
};

export default AdminTabsNav;
