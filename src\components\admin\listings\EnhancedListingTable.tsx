import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Edit, Trash2, Star, StarOff } from 'lucide-react';
import { useLanguage } from '@/hooks/useLanguage';
import { formatCurrency } from '@/utils/currency';
import { formatDate } from '@/utils/date';
import { ListingWithStatus, ListingStatus } from '@/types/admin';
import StatusBadge from './StatusBadge';
import { Link } from 'react-router-dom';

interface EnhancedListingTableProps {
  listings: ListingWithStatus[];
  selectedIds: string[];
  onSelectionChange: (ids: string[]) => void;
  onStatusChange: (id: string, status: ListingStatus) => Promise<void>;
  onFeatureToggle: (id: string, currentStatus: boolean) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
  isLoading: boolean;
}

const EnhancedListingTable: React.FC<EnhancedListingTableProps> = ({
  listings,
  selectedIds,
  onSelectionChange,
  onStatusChange,
  onFeatureToggle,
  onDelete,
  isLoading
}) => {
  const { t } = useLanguage();
  const [statusChanging, setStatusChanging] = useState<string | null>(null);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(listings.map(listing => listing.id));
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectOne = (id: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedIds, id]);
    } else {
      onSelectionChange(selectedIds.filter(selectedId => selectedId !== id));
    }
  };

  const handleStatusChange = async (id: string, newStatus: ListingStatus) => {
    setStatusChanging(id);
    try {
      await onStatusChange(id, newStatus);
    } finally {
      setStatusChanging(null);
    }
  };

  const isAllSelected = listings.length > 0 && selectedIds.length === listings.length;
  const isIndeterminate = selectedIds.length > 0 && selectedIds.length < listings.length;

  if (listings.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        {t('admin.noData')}
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={isAllSelected}
                onCheckedChange={handleSelectAll}
                aria-label={t('admin.selectAll')}
                {...(isIndeterminate && { 'data-state': 'indeterminate' })}
              />
            </TableHead>
            <TableHead>{t('admin.title')}</TableHead>
            <TableHead>{t('admin.make')}</TableHead>
            <TableHead>{t('admin.model')}</TableHead>
            <TableHead>{t('admin.year')}</TableHead>
            <TableHead>{t('admin.price')}</TableHead>
            <TableHead>{t('admin.location')}</TableHead>
            <TableHead>{t('admin.status')}</TableHead>
            <TableHead>{t('admin.created')}</TableHead>
            <TableHead>{t('admin.actions')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {listings.map((listing) => (
            <TableRow key={listing.id}>
              <TableCell>
                <Checkbox
                  checked={selectedIds.includes(listing.id)}
                  onCheckedChange={(checked) => handleSelectOne(listing.id, checked as boolean)}
                  aria-label={`Select ${listing.title}`}
                />
              </TableCell>
              <TableCell className="font-medium">
                <Link 
                  to={`/listings/${listing.id}`} 
                  className="hover:underline text-primary"
                >
                  {listing.title}
                </Link>
                {listing.featured && (
                  <Star className="inline w-4 h-4 ml-2 text-yellow-500 fill-yellow-500" />
                )}
              </TableCell>
              <TableCell>{listing.make}</TableCell>
              <TableCell>{listing.model}</TableCell>
              <TableCell>{listing.year}</TableCell>
              <TableCell>{formatCurrency(listing.price)}</TableCell>
              <TableCell>{listing.location}</TableCell>
              <TableCell>
                <Select
                  value={listing.status}
                  onValueChange={(value) => handleStatusChange(listing.id, value as ListingStatus)}
                  disabled={statusChanging === listing.id || isLoading}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue>
                      <StatusBadge status={listing.status} />
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">
                      <StatusBadge status="pending" />
                    </SelectItem>
                    <SelectItem value="approved">
                      <StatusBadge status="approved" />
                    </SelectItem>
                    <SelectItem value="rejected">
                      <StatusBadge status="rejected" />
                    </SelectItem>
                    <SelectItem value="blocked">
                      <StatusBadge status="blocked" />
                    </SelectItem>
                    <SelectItem value="active">
                      <StatusBadge status="active" />
                    </SelectItem>
                  </SelectContent>
                </Select>
              </TableCell>
              <TableCell>{formatDate(listing.created_at)}</TableCell>
              <TableCell>
                <div className="flex space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => onFeatureToggle(listing.id, listing.featured || false)}
                    title={listing.featured ? t('admin.unfeature') : t('admin.feature')}
                    disabled={isLoading}
                  >
                    {listing.featured ? (
                      <StarOff className="h-4 w-4" />
                    ) : (
                      <Star className="h-4 w-4" />
                    )}
                  </Button>
                  
                  <Link to={`/edit-listing/${listing.id}`}>
                    <Button variant="outline" size="sm" title={t('admin.edit')}>
                      <Edit className="h-4 w-4" />
                    </Button>
                  </Link>
                  
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        title={t('admin.delete')}
                        disabled={isLoading}
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>{t('admin.confirmDelete')}</AlertDialogTitle>
                        <AlertDialogDescription>
                          {t('admin.deleteListingWarning')}
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>{t('admin.no')}</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => onDelete(listing.id)}
                          className="bg-destructive text-destructive-foreground"
                        >
                          {t('admin.yes')}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default EnhancedListingTable;
