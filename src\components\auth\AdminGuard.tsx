import React, { ReactNode } from 'react';
import { useAdminAccess, useAdminPermissions } from '@/hooks/useAdminAccess';

interface AdminGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
  permission?: keyof ReturnType<typeof useAdminPermissions>['permissions'];
  requireAll?: boolean;
  permissions?: (keyof ReturnType<typeof useAdminPermissions>['permissions'])[];
}

/**
 * Компонент для условного отображения административных элементов UI
 * Скрывает содержимое от пользователей без административных прав
 */
export const AdminGuard: React.FC<AdminGuardProps> = ({
  children,
  fallback = null,
  permission,
  permissions = [],
  requireAll = false,
}) => {
  const { isAdmin, isLoading } = useAdminAccess();
  const { hasPermission, hasAnyPermission, hasAllPermissions } = useAdminPermissions();

  // Показываем fallback во время загрузки
  if (isLoading) {
    return <>{fallback}</>;
  }

  // Если пользователь не администратор, не показываем содержимое
  if (!isAdmin) {
    return <>{fallback}</>;
  }

  // Проверяем конкретное разрешение
  if (permission && !hasPermission(permission)) {
    return <>{fallback}</>;
  }

  // Проверяем список разрешений
  if (permissions.length > 0) {
    const hasRequiredPermissions = requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);
    
    if (!hasRequiredPermissions) {
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
};

/**
 * Хук для условного рендеринга административных элементов
 */
export const useAdminGuard = () => {
  const { isAdmin, isLoading } = useAdminAccess();
  const { hasPermission, hasAnyPermission, hasAllPermissions } = useAdminPermissions();

  const renderIfAdmin = (content: ReactNode, fallback: ReactNode = null) => {
    if (isLoading) return fallback;
    return isAdmin ? content : fallback;
  };

  const renderIfPermission = (
    permission: keyof ReturnType<typeof useAdminPermissions>['permissions'],
    content: ReactNode,
    fallback: ReactNode = null
  ) => {
    if (isLoading) return fallback;
    return hasPermission(permission) ? content : fallback;
  };

  const renderIfAnyPermission = (
    permissions: (keyof ReturnType<typeof useAdminPermissions>['permissions'])[],
    content: ReactNode,
    fallback: ReactNode = null
  ) => {
    if (isLoading) return fallback;
    return hasAnyPermission(permissions) ? content : fallback;
  };

  const renderIfAllPermissions = (
    permissions: (keyof ReturnType<typeof useAdminPermissions>['permissions'])[],
    content: ReactNode,
    fallback: ReactNode = null
  ) => {
    if (isLoading) return fallback;
    return hasAllPermissions(permissions) ? content : fallback;
  };

  return {
    isAdmin,
    isLoading,
    renderIfAdmin,
    renderIfPermission,
    renderIfAnyPermission,
    renderIfAllPermissions,
  };
};

export default AdminGuard;
