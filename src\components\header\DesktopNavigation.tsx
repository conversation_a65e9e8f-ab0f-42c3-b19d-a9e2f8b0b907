
import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

const DesktopNavigation: React.FC = () => {
  const { t } = useLanguage();
  const location = useLocation();
  const { isAuthenticated, user } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!isAuthenticated || !user) {
        setIsAdmin(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('is_admin')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error checking admin status:', error);
          setIsAdmin(false);
          return;
        }

        setIsAdmin(data?.is_admin === true);
      } catch (error) {
        console.error('Error checking admin status:', error);
        setIsAdmin(false);
      }
    };

    checkAdminStatus();
  }, [isAuthenticated, user]);

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="hidden md:flex items-center space-x-8">
      <Link
        to="/"
        className={`text-sm font-medium transition-colors hover:text-primary ${
          isActive('/') ? 'text-primary' : 'text-foreground/80'
        }`}
      >
        {t('home')}
      </Link>
      <Link
        to="/listings"
        className={`text-sm font-medium transition-colors hover:text-primary ${
          isActive('/listings') ? 'text-primary' : 'text-foreground/80'
        }`}
      >
        {t('listingsNav')}
      </Link>
      {isAuthenticated && isAdmin && (
        <Link
          to="/admin"
          className={`text-sm font-medium transition-colors hover:text-primary ${
            isActive('/admin') ? 'text-primary' : 'text-foreground/80'
          }`}
        >
          {t('admin.title')}
        </Link>
      )}
    </nav>
  );
};

export default DesktopNavigation;
