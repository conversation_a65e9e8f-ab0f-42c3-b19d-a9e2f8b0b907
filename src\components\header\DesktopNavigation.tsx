
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { AdminGuard } from '@/components/auth/AdminGuard';
import { useAdminAccess } from '@/hooks/useAdminAccess';

const DesktopNavigation: React.FC = () => {
  const { t } = useLanguage();
  const location = useLocation();
  const { isAdmin, isLoading } = useAdminAccess();

  // Отладочная информация
  console.log('DesktopNavigation - Admin status:', { isAdmin, isLoading });

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="hidden md:flex items-center space-x-8">
      <Link
        to="/"
        className={`text-sm font-medium transition-colors hover:text-primary ${
          isActive('/') ? 'text-primary' : 'text-foreground/80'
        }`}
      >
        {t('home')}
      </Link>
      <Link
        to="/listings"
        className={`text-sm font-medium transition-colors hover:text-primary ${
          isActive('/listings') ? 'text-primary' : 'text-foreground/80'
        }`}
      >
        {t('listingsNav')}
      </Link>

      {/* Простая проверка без AdminGuard для отладки */}
      {isAdmin && (
        <Link
          to="/admin"
          className={`text-sm font-medium transition-colors hover:text-primary ${
            isActive('/admin') ? 'text-primary' : 'text-foreground/80'
          }`}
        >
          {t('admin.title')} (Direct)
        </Link>
      )}

      {/* Проверка с AdminGuard */}
      <AdminGuard>
        <Link
          to="/admin"
          className={`text-sm font-medium transition-colors hover:text-primary ${
            isActive('/admin') ? 'text-primary' : 'text-foreground/80'
          }`}
        >
          {t('admin.title')} (Guard)
        </Link>
      </AdminGuard>

      {/* Отладочная информация */}
      {!isLoading && (
        <span className="text-xs text-muted-foreground">
          Admin: {isAdmin ? 'Yes' : 'No'}
        </span>
      )}
    </nav>
  );
};

export default DesktopNavigation;
