
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { AdminGuard } from '@/components/auth/AdminGuard';

const DesktopNavigation: React.FC = () => {
  const { t } = useLanguage();
  const location = useLocation();

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="hidden md:flex items-center space-x-8">
      <Link
        to="/"
        className={`text-sm font-medium transition-colors hover:text-primary ${
          isActive('/') ? 'text-primary' : 'text-foreground/80'
        }`}
      >
        {t('home')}
      </Link>
      <Link
        to="/listings"
        className={`text-sm font-medium transition-colors hover:text-primary ${
          isActive('/listings') ? 'text-primary' : 'text-foreground/80'
        }`}
      >
        {t('listingsNav')}
      </Link>
      <AdminGuard>
        <Link
          to="/admin"
          className={`text-sm font-medium transition-colors hover:text-primary ${
            isActive('/admin') ? 'text-primary' : 'text-foreground/80'
          }`}
        >
          {t('admin.title')}
        </Link>
      </AdminGuard>
    </nav>
  );
};

export default DesktopNavigation;
