
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { Button } from '@/components/ui/button';
import { User, LogOut, MessageSquare, LayoutDashboard } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface MobileMenuProps {
  isOpen: boolean;
}

const MobileMenu: React.FC<MobileMenuProps> = ({ isOpen }) => {
  const { t, language, setLanguage } = useLanguage();
  const location = useLocation();
  const { user, signOut, isAuthenticated } = useAuth();

  const isActive = (path: string) => location.pathname === path;

  if (!isOpen) return null;

  return (
    <div className="md:hidden bg-background border-t border-border animate-fade-in">
      <div className="px-4 pt-2 pb-3 space-y-1 sm:px-6">
        <Link
          to="/"
          className={`block px-3 py-2 rounded-md text-base font-medium ${
            isActive('/')
              ? 'bg-primary/10 text-primary'
              : 'hover:bg-secondary hover:text-foreground'
          }`}
        >
          {t('home')}
        </Link>
        <Link
          to="/listings"
          className={`block px-3 py-2 rounded-md text-base font-medium ${
            isActive('/listings')
              ? 'bg-primary/10 text-primary'
              : 'hover:bg-secondary hover:text-foreground'
          }`}
        >
          {t('listingsNav')}
        </Link>

        {isAuthenticated && (
          <>
            <Link
              to="/messages"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                isActive('/messages')
                  ? 'bg-primary/10 text-primary'
                  : 'hover:bg-secondary hover:text-foreground'
              }`}
            >
              {t('messages')}
            </Link>
            <Link
              to="/admin"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                isActive('/admin')
                  ? 'bg-primary/10 text-primary'
                  : 'hover:bg-secondary hover:text-foreground'
              }`}
            >
              {t('admin.title')}
            </Link>
          </>
        )}

        {/* Language Selector */}
        <div className="px-3 py-2">
          <div className="text-sm font-medium text-muted-foreground mb-2">
            {t('language')}
          </div>
          <div className="flex space-x-2">
            <Button
              variant={language === 'en' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setLanguage('en')}
            >
              EN
            </Button>
            <Button
              variant={language === 'ru' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setLanguage('ru')}
            >
              RU
            </Button>
          </div>
        </div>

        <div className="pt-4 pb-3 border-t border-border">
          {isAuthenticated ? (
            <>
              <div className="flex items-center px-3">
                <div className="flex-shrink-0">
                  <div className="h-10 w-10 rounded-full bg-primary text-primary-foreground flex items-center justify-center">
                    {user?.email?.charAt(0).toUpperCase() || "U"}
                  </div>
                </div>
                <div className="ml-3">
                  <div className="text-base font-medium">{user?.email}</div>
                </div>
              </div>
              <div className="mt-3 space-y-1 px-3">
                <Link
                  to="/profile"
                  className="flex items-center px-3 py-2 rounded-md text-base font-medium hover:bg-secondary"
                >
                  <User className="mr-2 h-4 w-4" />
                  {t('profile.profile')}
                </Link>
                <button
                  onClick={() => signOut()}
                  className="flex items-center w-full text-left px-3 py-2 rounded-md text-base font-medium text-destructive hover:bg-secondary"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  {t('signOut')}
                </button>
              </div>
            </>
          ) : (
            <>
              <div className="flex items-center px-3">
                <div className="flex-shrink-0">
                  <User className="h-10 w-10 rounded-full bg-secondary p-2" />
                </div>
                <div className="ml-3">
                  <div className="text-base font-medium">{t('signIn')}</div>
                </div>
              </div>
              <div className="mt-3 space-y-1 px-3">
                <Link
                  to="/auth"
                  className="block px-3 py-2 rounded-md text-base font-medium hover:bg-secondary"
                >
                  {t('signIn')}
                </Link>
                <Link
                  to="/auth?mode=signup"
                  className="block px-3 py-2 rounded-md text-base font-medium hover:bg-secondary"
                >
                  {t('signUp')}
                </Link>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default MobileMenu;
