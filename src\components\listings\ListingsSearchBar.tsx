
import React, { useState, useEffect } from 'react';
import { RefreshCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import SearchBar from '@/components/listings/SearchBar';
import SortSelector, { SortOption } from '@/components/listings/SortSelector';
import SaveSearchButton from '@/components/listings/SaveSearchButton';
import { useLanguage } from '@/hooks/useLanguage';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { carMakes, carModelsByMake, defaultModels, carBodyTypes, carFuelTypes, carTransmissionTypes, carColors } from '@/utils/dictionaries/vehicles';

interface ListingsSearchBarProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  sortBy: SortOption;
  onSortChange: (value: SortOption) => void;
  onFilterApply: (filters: Record<string, any>) => void;
  onFilterReset: () => void;
  onRefresh: () => void;
  filters: Record<string, any>;
}

const ListingsSearchBar: React.FC<ListingsSearchBarProps> = ({
  searchTerm,
  onSearchChange,
  sortBy,
  onSortChange,
  onFilterApply,
  onFilterReset,
  onRefresh,
  filters
}) => {
  const { t, language } = useLanguage();
  const [make, setMake] = useState<string>(filters.make || '');
  const [model, setModel] = useState<string>(filters.model || '');
  const [priceMin, setPriceMin] = useState<string>(filters.minPrice || '');
  const [priceMax, setPriceMax] = useState<string>(filters.maxPrice || '');
  const [yearFrom, setYearFrom] = useState<string>(filters.minYear || '');
  const [yearTo, setYearTo] = useState<string>(filters.maxYear || '');
  const [location, setLocation] = useState<string>(filters.location || '');
  const [modelOptions, setModelOptions] = useState<string[]>([]);

  // New car filter states
  const [bodyType, setBodyType] = useState<string>(filters.body_type || '');
  const [fuelType, setFuelType] = useState<string>(filters.fuel_type || '');
  const [transmission, setTransmission] = useState<string>(filters.transmission || '');
  const [color, setColor] = useState<string>(filters.color || '');
  const [mileageMin, setMileageMin] = useState<string>(filters.mileage_min || '');
  const [mileageMax, setMileageMax] = useState<string>(filters.mileage_max || '');

  // Get localized options
  const bodyTypeOptions = carBodyTypes[language as keyof typeof carBodyTypes] || carBodyTypes.en;
  const fuelTypeOptions = carFuelTypes[language as keyof typeof carFuelTypes] || carFuelTypes.en;
  const transmissionOptions = carTransmissionTypes[language as keyof typeof carTransmissionTypes] || carTransmissionTypes.en;
  const colorOptions = carColors[language as keyof typeof carColors] || carColors.en;

  // Create year options
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: 21 }, (_, i) => (currentYear - i).toString());

  // Update model options when make changes
  useEffect(() => {
    if (make && make !== 'all') {
      setModelOptions(carModelsByMake[make] || defaultModels);
    } else {
      setModelOptions([]);
      setModel(''); // Reset model when make changes
    }
  }, [make]);

  const handleApplyFilters = () => {
    const newFilters: Record<string, any> = {};

    if (make && make !== 'all') {
      newFilters.make = make;
    }

    if (model && model !== 'all_models') {
      newFilters.model = model;
    }

    if (yearFrom && yearFrom !== 'any_year_from') {
      newFilters.minYear = Number(yearFrom);
    }

    if (yearTo && yearTo !== 'any_year_to') {
      newFilters.maxYear = Number(yearTo);
    }

    if (priceMin) {
      newFilters.minPrice = Number(priceMin);
    }

    if (priceMax) {
      newFilters.maxPrice = Number(priceMax);
    }

    if (location) {
      newFilters.location = location;
    }

    // Add new car filters
    if (bodyType && bodyType !== 'all') {
      newFilters.body_type = bodyType;
    }

    if (fuelType && fuelType !== 'all') {
      newFilters.fuel_type = fuelType;
    }

    if (transmission && transmission !== 'all') {
      newFilters.transmission = transmission;
    }

    if (color && color !== 'all') {
      newFilters.color = color;
    }

    if (mileageMin) {
      newFilters.mileage_min = Number(mileageMin);
    }

    if (mileageMax) {
      newFilters.mileage_max = Number(mileageMax);
    }

    onFilterApply(newFilters);
  };

  const handleReset = () => {
    setMake('');
    setModel('');
    setYearFrom('');
    setYearTo('');
    setPriceMin('');
    setPriceMax('');
    setLocation('');

    // Reset new car filters too
    setBodyType('');
    setFuelType('');
    setTransmission('');
    setColor('');
    setMileageMin('');
    setMileageMax('');

    onFilterReset();
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col md:flex-row gap-4">
        <SearchBar
          searchTerm={searchTerm}
          onSearchChange={onSearchChange}
          placeholder={t('ui.search')}
        />

        <div className="flex gap-3">
          <SortSelector value={sortBy} onChange={onSortChange} />

          <Button variant="outline" className="h-10" onClick={onRefresh}>
            <RefreshCcw className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
        {/* Make filter */}
        <Select value={make} onValueChange={setMake}>
          <SelectTrigger>
            <SelectValue placeholder={t('listings.selectMake')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('listings.all')}</SelectItem>
            {carMakes.map((carMake) => (
              <SelectItem key={carMake} value={carMake}>
                {carMake}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Model filter - conditionally rendered */}
        {make && make !== 'all' && (
          <Select value={model} onValueChange={setModel}>
            <SelectTrigger>
              <SelectValue placeholder={t('listings.selectModel')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all_models">{t('listings.all')}</SelectItem>
              {modelOptions.map((carModel) => (
                <SelectItem key={carModel} value={carModel}>
                  {carModel}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        {/* Price filter */}
        <div className="flex gap-2">
          <Input
            placeholder={t('listings.min')}
            type="number"
            value={priceMin}
            onChange={(e) => setPriceMin(e.target.value)}
          />
          <Input
            placeholder={t('listings.max')}
            type="number"
            value={priceMax}
            onChange={(e) => setPriceMax(e.target.value)}
          />
        </div>

        {/* Year filter */}
        <div className="flex gap-2">
          <Select value={yearFrom} onValueChange={setYearFrom}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder={t('listings.yearFrom')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="any_year_from">{t('listings.any')}</SelectItem>
              {yearOptions.map((year) => (
                <SelectItem key={`from-${year}`} value={year}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={yearTo} onValueChange={setYearTo}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder={t('listings.yearTo')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="any_year_to">{t('listings.any')}</SelectItem>
              {yearOptions.map((year) => (
                <SelectItem key={`to-${year}`} value={year}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Body Type filter */}
        <Select value={bodyType} onValueChange={setBodyType}>
          <SelectTrigger>
            <SelectValue placeholder={t('listings.bodyType')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('listings.all')}</SelectItem>
            {bodyTypeOptions.map((type) => (
              <SelectItem key={type} value={type}>
                {type}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Fuel Type filter */}
        <Select value={fuelType} onValueChange={setFuelType}>
          <SelectTrigger>
            <SelectValue placeholder={t('listings.fuelType')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('listings.all')}</SelectItem>
            {fuelTypeOptions.map((type) => (
              <SelectItem key={type} value={type}>
                {type}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Transmission filter */}
        <Select value={transmission} onValueChange={setTransmission}>
          <SelectTrigger>
            <SelectValue placeholder={t('listings.transmission')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('listings.all')}</SelectItem>
            {transmissionOptions.map((type) => (
              <SelectItem key={type} value={type}>
                {type}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Color filter */}
        <Select value={color} onValueChange={setColor}>
          <SelectTrigger>
            <SelectValue placeholder={t('listings.color')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('listings.all')}</SelectItem>
            {colorOptions.map((colorOption) => (
              <SelectItem key={colorOption} value={colorOption}>
                {colorOption}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Mileage filter */}
        <div className="flex gap-2">
          <Input
            placeholder={t('listings.min')}
            type="number"
            value={mileageMin}
            onChange={(e) => setMileageMin(e.target.value)}
          />
          <Input
            placeholder={t('listings.max')}
            type="number"
            value={mileageMax}
            onChange={(e) => setMileageMax(e.target.value)}
          />
        </div>

        {/* Location filter */}
        <Input
          placeholder={t('listings.location')}
          value={location}
          onChange={(e) => setLocation(e.target.value)}
        />

        {/* Actions */}
        <div className="flex gap-2 col-span-1 sm:col-span-2 md:col-span-3 lg:col-span-4">
          <Button onClick={handleApplyFilters} className="flex-grow">{t('listings.applyFilters')}</Button>
          <Button variant="outline" onClick={handleReset}>{t('listings.reset')}</Button>
        </div>
      </div>

      <div className="flex justify-end">
        <SaveSearchButton
          searchTerm={searchTerm}
          filters={filters}
        />
      </div>
    </div>
  );
};

export default ListingsSearchBar;
