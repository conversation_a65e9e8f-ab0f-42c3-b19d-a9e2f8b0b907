
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { carFuelTypes } from '@/utils/dictionaries/vehicles';

interface FuelTypeFilterProps {
  fuelType: string;
  setFuelType: (value: string) => void;
}

const FuelTypeFilter: React.FC<FuelTypeFilterProps> = ({
  fuelType,
  setFuelType
}) => {
  const { t } = useLanguage();

  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium">{t('listings.fuelType')}</h3>
      <Select value={fuelType} onValueChange={setFuelType}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder={t('listings.selectFuelType')} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">{t('listings.all')}</SelectItem>
          {carFuelTypes.map((type) => (
            <SelectItem key={type} value={type}>
              {type}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default FuelTypeFilter;
