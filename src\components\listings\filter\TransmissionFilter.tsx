
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { carTransmissionTypes } from '@/utils/dictionaries/vehicles';

interface TransmissionFilterProps {
  transmission: string;
  setTransmission: (value: string) => void;
}

const TransmissionFilter: React.FC<TransmissionFilterProps> = ({
  transmission,
  setTransmission
}) => {
  const { t } = useLanguage();

  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium">{t('listings.transmission')}</h3>
      <Select value={transmission} onValueChange={setTransmission}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder={t('listings.selectTransmission')} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">{t('listings.all')}</SelectItem>
          {carTransmissionTypes.map((type) => (
            <SelectItem key={type} value={type}>
              {type}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default TransmissionFilter;
