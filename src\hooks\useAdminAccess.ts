import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

interface AdminAccessState {
  isAdmin: boolean;
  isLoading: boolean;
  error: string | null;
}

/**
 * Централизованный хук для проверки административных прав доступа
 * Поддерживает как поле is_admin, так и role = 'admin' для обратной совместимости
 */
export const useAdminAccess = () => {
  const { user, isAuthenticated } = useAuth();
  const [state, setState] = useState<AdminAccessState>({
    isAdmin: false,
    isLoading: true,
    error: null,
  });

  const checkAdminStatus = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setState({
        isAdmin: false,
        isLoading: false,
        error: null,
      });
      return;
    }

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Используем функцию из базы данных для проверки прав администратора
      const { data, error } = await supabase.rpc('is_user_admin', {
        user_id: user.id
      });

      if (error) {
        console.error('Error checking admin status:', error);
        
        // Fallback: проверяем напрямую через таблицу profiles
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('is_admin, role')
          .eq('id', user.id)
          .single();

        if (profileError) {
          throw profileError;
        }

        const isAdmin = profileData?.is_admin === true || profileData?.role === 'admin';
        setState({
          isAdmin,
          isLoading: false,
          error: null,
        });
        return;
      }

      setState({
        isAdmin: data === true,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      console.error('Error checking admin status:', error);
      setState({
        isAdmin: false,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }, [isAuthenticated, user]);

  useEffect(() => {
    checkAdminStatus();
  }, [checkAdminStatus]);

  // Функция для принудительного обновления статуса
  const refreshAdminStatus = useCallback(() => {
    checkAdminStatus();
  }, [checkAdminStatus]);

  return {
    isAdmin: state.isAdmin,
    isLoading: state.isLoading,
    error: state.error,
    refreshAdminStatus,
  };
};

/**
 * Хук для проверки конкретных административных разрешений
 */
export const useAdminPermissions = () => {
  const { isAdmin, isLoading } = useAdminAccess();

  const permissions = {
    // Управление пользователями
    canManageUsers: isAdmin,
    canViewUsers: isAdmin,
    canEditUsers: isAdmin,
    canDeleteUsers: isAdmin,
    
    // Управление объявлениями
    canManageListings: isAdmin,
    canApproveListings: isAdmin,
    canDeleteListings: isAdmin,
    canViewAllListings: isAdmin,
    
    // Управление отчетами
    canViewReports: isAdmin,
    canManageReports: isAdmin,
    
    // Системные настройки
    canManageSettings: isAdmin,
    canViewAnalytics: isAdmin,
    
    // Уведомления
    canManageNotifications: isAdmin,
    canViewAdminNotifications: isAdmin,
  };

  return {
    permissions,
    isLoading,
    hasPermission: (permission: keyof typeof permissions) => permissions[permission],
    hasAnyPermission: (permissionList: (keyof typeof permissions)[]) => 
      permissionList.some(permission => permissions[permission]),
    hasAllPermissions: (permissionList: (keyof typeof permissions)[]) => 
      permissionList.every(permission => permissions[permission]),
  };
};

export default useAdminAccess;
