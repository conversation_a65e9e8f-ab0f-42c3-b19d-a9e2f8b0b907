
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 220 10% 15%;

    --card: 0 0% 100%;
    --card-foreground: 220 10% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 10% 15%;

    --primary: 216 100% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 216 10% 95%;
    --secondary-foreground: 220 10% 15%;

    --muted: 216 10% 95%;
    --muted-foreground: 220 10% 45%;

    --accent: 216 100% 50%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 216 20% 90%;
    --input: 216 20% 90%;
    --ring: 216 100% 50%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5% 26%;
    --sidebar-primary: 240 6% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 5% 96%;
    --sidebar-accent-foreground: 240 6% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 92% 60%;
  }

  .dark {
    --background: 220 10% 10%;
    --foreground: 220 10% 98%;

    --card: 220 10% 12%;
    --card-foreground: 220 10% 98%;

    --popover: 220 10% 12%;
    --popover-foreground: 220 10% 98%;

    --primary: 216 100% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 220 10% 17%;
    --secondary-foreground: 220 10% 98%;

    --muted: 220 10% 17%;
    --muted-foreground: 220 10% 70%;

    --accent: 216 100% 50%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 10% 20%;
    --input: 220 10% 20%;
    --ring: 216 100% 50%;

    --sidebar-background: 240 6% 10%;
    --sidebar-foreground: 240 5% 96%;
    --sidebar-primary: 216 100% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4% 16%;
    --sidebar-accent-foreground: 240 5% 96%;
    --sidebar-border: 240 4% 16%;
    --sidebar-ring: 217 92% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-medium tracking-tight;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  h2 {
    @apply text-3xl md:text-4xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  .glass {
    @apply bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl;
  }

  .glass-card {
    @apply bg-white/70 dark:bg-black/40 backdrop-blur-lg border border-white/20 dark:border-white/10 rounded-xl shadow-glass;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-elevated hover:-translate-y-1;
  }

  .page-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8;
  }

  .section {
    @apply py-12 md:py-16 lg:py-20;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-secondary rounded-full;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/40 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/60;
  }

  /* Disable iOS tap highlight */
  input,
  textarea,
  button,
  select,
  a {
    -webkit-tap-highlight-color: transparent;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
