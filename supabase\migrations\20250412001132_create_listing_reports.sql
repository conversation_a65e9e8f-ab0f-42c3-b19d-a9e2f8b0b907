-- Create listing_reports table
CREATE TABLE IF NOT EXISTS public.listing_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    listing_id UUID NOT NULL REFERENCES public.listings(id) ON DELETE CASCADE,
    reason TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE public.listing_reports ENABLE ROW LEVEL SECURITY;

-- Allow users to create reports
CREATE POLICY "Users can create reports" ON public.listing_reports
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

-- Allow users to view their own reports
CREATE POLICY "Users can view their own reports" ON public.listing_reports
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

-- Allow admins to view all reports
CREATE POLICY "Ad<PERSON> can view all reports" ON public.listing_reports
    FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Allow admins to update reports
CREATE POLICY "Admins can update reports" ON public.listing_reports
    FOR UPDATE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Create function to notify admins of new reports
CREATE OR REPLACE FUNCTION public.handle_new_report()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert notification for admins
    INSERT INTO public.admin_notifications (type, message, data, read)
    VALUES (
        'listing_report',
        'New listing report submitted',
        jsonb_build_object(
            'report_id', NEW.id,
            'listing_id', NEW.listing_id,
            'user_id', NEW.user_id,
            'reason', NEW.reason
        ),
        false
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new reports
CREATE TRIGGER on_new_report
AFTER INSERT ON public.listing_reports
FOR EACH ROW
EXECUTE FUNCTION public.handle_new_report();